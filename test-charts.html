<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Test</title>
    <script src="https://code.highcharts.com/highcharts.js"></script>
</head>
<body>
    <h1>Chart Test</h1>
    <div id="test-container" style="width: 400px; height: 300px; border: 1px solid #ccc; margin: 20px;"></div>
    
    <script>
        // Test if Highcharts is available
        if (typeof Highcharts !== 'undefined') {
            console.log('Highcharts is available!');
            
            // Create a simple test chart
            Highcharts.chart('test-container', {
                chart: {
                    type: 'line'
                },
                title: {
                    text: 'Test Chart'
                },
                xAxis: {
                    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May']
                },
                yAxis: {
                    title: {
                        text: 'Values'
                    }
                },
                series: [{
                    name: 'Test Data',
                    data: [1, 3, 2, 4, 5]
                }]
            });
        } else {
            console.error('Highcharts is not available!');
            document.getElementById('test-container').innerHTML = '<p style="color: red;">Highcharts failed to load</p>';
        }
    </script>
</body>
</html>
