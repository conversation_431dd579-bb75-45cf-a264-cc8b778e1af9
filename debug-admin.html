<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Dabang Admin Dashboard</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <h1>Debug Page</h1>
    
    <div style="padding: 20px; border: 1px solid #ccc; margin: 20px;">
        <h2>Testing Section Header</h2>
        <section class="sales-section">
            <header class="section-header">
                <div class="section-header-left">
                    <h2>Today's Sales</h2>
                    <p class="section-description">Sales summary</p>
                </div>
                <button type="button" class="export-btn">
                    <span>Export</span>
                </button>
            </header>
            <div class="sales-cards">
                <article class="sales-card">
                    <div class="sales-card-content">
                        <data value="1000" class="metric-value">$1k</data>
                        <h3 class="metric-label">Total Sales</h3>
                    </div>
                    <p class="metric-change">+8% from yesterday</p>
                </article>
            </div>
        </section>
    </div>

    <div style="padding: 20px; border: 1px solid #ccc; margin: 20px;">
        <h2>Testing Chart Container</h2>
        <div id="debug-container" style="width: 400px; height: 300px; border: 1px solid #ddd;"></div>
    </div>

    <div id="debug-info" style="padding: 20px; background: #f0f0f0; margin: 20px;">
        <h3>Debug Information</h3>
        <p id="highcharts-status">Checking Highcharts...</p>
        <p id="css-status">Checking CSS...</p>
    </div>

    <!-- Highcharts Scripts -->
    <script src="https://code.highcharts.com/highcharts.js"></script>
    
    <script>
        // Debug script
        document.addEventListener('DOMContentLoaded', function() {
            const highchartsStatus = document.getElementById('highcharts-status');
            const cssStatus = document.getElementById('css-status');
            
            // Check Highcharts
            if (typeof Highcharts !== 'undefined') {
                highchartsStatus.textContent = '✅ Highcharts is loaded successfully';
                highchartsStatus.style.color = 'green';
                
                // Try to create a simple chart
                try {
                    Highcharts.chart('debug-container', {
                        chart: { type: 'line' },
                        title: { text: 'Debug Chart' },
                        series: [{ name: 'Test', data: [1, 2, 3, 4, 5] }]
                    });
                } catch (error) {
                    highchartsStatus.textContent = '❌ Highcharts loaded but chart creation failed: ' + error.message;
                    highchartsStatus.style.color = 'red';
                }
            } else {
                highchartsStatus.textContent = '❌ Highcharts is not loaded';
                highchartsStatus.style.color = 'red';
            }
            
            // Check CSS
            const salesSection = document.querySelector('.sales-section');
            if (salesSection) {
                const styles = window.getComputedStyle(salesSection);
                if (styles.backgroundColor && styles.backgroundColor !== 'rgba(0, 0, 0, 0)') {
                    cssStatus.textContent = '✅ CSS is working - background color: ' + styles.backgroundColor;
                    cssStatus.style.color = 'green';
                } else {
                    cssStatus.textContent = '❌ CSS not applied properly';
                    cssStatus.style.color = 'red';
                }
            } else {
                cssStatus.textContent = '❌ Sales section element not found';
                cssStatus.style.color = 'red';
            }
        });
    </script>
</body>
</html>
