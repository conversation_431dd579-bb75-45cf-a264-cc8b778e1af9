<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Dabang Admin Dashboard - Comprehensive analytics and management interface for sales, orders, and customer insights">
    <meta name="keywords" content="admin, dashboard, analytics, sales, orders, management">
    <meta name="author" content="Dabang">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/svg+xml" href="./assets/images/dummy.svg">
    <link rel="apple-touch-icon" href="./assets/images/dummy.svg">

    <!-- Theme and App Manifest -->
    <meta name="theme-color" content="#5d5fef">
    <meta name="color-scheme" content="light">
    <title>Dabang Admin Dashboard - Analytics & Management</title>
    <!-- CSS Resources -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reset-css@5.0.2/reset.min.css">
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
<svg width="0" height="0" class="hidden">
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="AMERICA">
    <g clip-path="url(#clip0_8121_115)">
      <path d="M12 0.75C9.88129 0.751693 7.80612 1.35165 6.01337 2.4808H17.9702C16.1822 1.35461 14.1131 0.754774 12 0.75Z" fill="#B22234"></path>
      <path d="M6.01337 2.48083C5.24109 2.96973 4.53126 3.55092 3.8996 4.21155H20.1137C19.4728 3.54901 18.7529 2.96775 17.9702 2.48083H6.01337Z" fill="white"></path>
      <path d="M3.8996 4.21155C3.38899 4.7424 2.93174 5.32212 2.53445 5.94235H21.4785C21.0813 5.32214 20.6242 4.74242 20.1137 4.21155H3.8996Z" fill="#B22234"></path>
      <path d="M2.53445 5.94232C2.17937 6.4915 1.87285 7.0706 1.61838 7.67303H22.3734C22.1257 7.07133 21.8263 6.49226 21.4785 5.94232H2.53445Z" fill="white"></path>
      <path d="M1.61838 7.67303C1.38803 8.23415 1.20344 8.81297 1.06641 9.40384H22.9328C22.7933 8.81275 22.6063 8.23392 22.3734 7.67303H1.61838Z" fill="#B22234"></path>
      <path d="M1.06641 9.40381C0.930513 9.97277 0.839202 10.5515 0.793304 11.1346H23.2134C23.165 10.5513 23.0712 9.97256 22.9328 9.40381H1.06641Z" fill="white"></path>
      <path d="M0.793304 11.1348C0.767763 11.4226 0.753317 11.7112 0.75 12.0001C0.751095 12.2889 0.763311 12.5776 0.786621 12.8655H23.2067C23.2322 12.5777 23.2467 12.289 23.25 12.0001C23.2489 11.7113 23.2367 11.4226 23.2134 11.1348H0.793304Z" fill="#B22234"></path>
      <path d="M0.786621 12.8652C0.834987 13.4486 0.928808 14.0273 1.06723 14.596H22.9336C23.0695 14.0271 23.1608 13.4484 23.2067 12.8652H0.786621Z" fill="white"></path>
      <path d="M1.06723 14.5962C1.20667 15.1873 1.39374 15.7661 1.62662 16.327H22.3816C22.612 15.7659 22.7966 15.1871 22.9336 14.5962H1.06723Z" fill="#B22234"></path>
      <path d="M1.62662 16.327C1.87431 16.9287 2.17375 17.5077 2.52155 18.0577H21.4655C21.8206 17.5085 22.1271 16.9294 22.3816 16.327H1.62662Z" fill="white"></path>
      <path d="M2.52155 18.0577C2.91872 18.6779 3.37584 19.2576 3.88632 19.7885H20.1004C20.611 19.2576 21.0683 18.6779 21.4655 18.0577H2.52155Z" fill="#B22234"></path>
      <path d="M3.88632 19.7885C4.5272 20.451 5.24707 21.0323 6.02975 21.5192H17.9866C18.7589 21.0303 19.4687 20.4491 20.1004 19.7885H3.88632Z" fill="white"></path>
      <path d="M6.02975 21.5192C7.81779 22.6454 9.88686 23.2452 12 23.25C14.1187 23.2483 16.1939 22.6483 17.9866 21.5192H6.02975Z" fill="#B22234"></path>
      <path d="M12 0.75C9.01631 0.75 6.15483 1.93526 4.04505 4.04505C1.93526 6.15483 0.75 9.01631 0.75 12C0.751472 12.2973 0.764727 12.5944 0.789734 12.8906H12.8906V0.794586C12.5945 0.767959 12.2973 0.753086 12 0.75Z" fill="#3C3B6E"></path>
      <path d="M7.97371 1.50847C7.82382 1.56319 7.6751 1.62109 7.52767 1.68214L7.692 1.80702L7.58086 2.16618L7.87529 1.94636L8.17833 2.17671L8.06453 1.805L8.36757 1.57878H7.99523L7.97371 1.50847ZM9.882 1.20117L9.76512 1.57875H9.39406L9.69442 1.80704L9.58325 2.16616L9.87766 1.94635L10.1807 2.17669L10.0669 1.80505L10.37 1.57875H9.99764L9.882 1.20117ZM11.8844 1.20117L11.7675 1.57875H11.3964L11.6968 1.80704L11.5856 2.16616L11.88 1.94635L12.1831 2.17669L12.0693 1.80505L12.3723 1.57875H12L11.8844 1.20117ZM5.07918 3.14726C5.02628 3.18671 4.97373 3.22663 4.92153 3.26701L5.17476 3.45955L5.07918 3.14726ZM6.87843 2.484L6.76154 2.86158H6.39049L6.69084 3.08987L6.57967 3.44899L6.87408 3.22918L7.17715 3.45952L7.06334 3.08788L7.36637 2.86158H6.99406L6.87843 2.484ZM8.88081 2.484L8.76393 2.86158H8.39287L8.69323 3.08987L8.58206 3.44899L8.87647 3.22918L9.17953 3.45952L9.06573 3.08788L9.36876 2.86158H8.99644L8.88081 2.484ZM10.8832 2.484L10.7663 2.86158H10.3953L10.6956 3.08987L10.5844 3.44899L10.8789 3.22918L11.1819 3.45952L11.0681 3.08788L11.3711 2.86158H10.9988L10.8832 2.484ZM3.98119 4.11415C3.87022 4.23161 3.7618 4.35145 3.65599 4.47358L3.57607 4.73176L3.8705 4.51194L4.17354 4.74229L4.05974 4.37068L4.36278 4.14436H3.99044L3.98119 4.11415ZM5.87724 3.76678L5.76034 4.14436H5.38929L5.68964 4.37265L5.57847 4.73176L5.87289 4.51196L6.17596 4.7423L6.06215 4.37066L6.36518 4.14436H5.99286L5.87724 3.76678ZM7.87962 3.76678L7.76273 4.14436H7.39168L7.69203 4.37265L7.58086 4.73176L7.87527 4.51196L8.17834 4.7423L8.06453 4.37066L8.36756 4.14436H7.99525L7.87962 3.76678ZM9.882 3.76678L9.76512 4.14436H9.39406L9.69442 4.37265L9.58325 4.73176L9.87766 4.51196L10.1807 4.7423L10.0669 4.37066L10.37 4.14436H9.99764L9.882 3.76678ZM11.8844 3.76678L11.7675 4.14436H11.3964L11.6968 4.37265L11.5856 4.73176L11.88 4.51196L12.1831 4.7423L12.0693 4.37066L12.3723 4.14436H12L11.8844 3.76678ZM2.95864 5.32704C2.85498 5.46518 2.75452 5.60569 2.65734 5.74846L2.57494 6.0146L2.86928 5.79478L3.17241 6.02513L3.05861 5.65352L3.36156 5.4272H2.98931L2.95864 5.32704ZM4.87604 5.04961L4.75915 5.42718H4.3881L4.68845 5.65548L4.57728 6.01459L4.8717 5.79479L5.17477 6.02513L5.06096 5.65348L5.36399 5.42718H4.99167L4.87604 5.04961ZM6.87843 5.04961L6.76154 5.42718H6.39049L6.69084 5.65548L6.57967 6.01459L6.87408 5.79479L7.17715 6.02513L7.06334 5.65348L7.36637 5.42718H6.99406L6.87843 5.04961ZM8.88081 5.04961L8.76393 5.42718H8.39287L8.69323 5.65548L8.58206 6.01459L8.87647 5.79479L9.17953 6.02513L9.06573 5.65348L9.36876 5.42718H8.99644L8.88081 5.04961ZM10.8832 5.04961L10.7663 5.42718H10.3953L10.6956 5.65548L10.5844 6.01459L10.8789 5.79479L11.1819 6.02513L11.0681 5.65348L11.3711 5.42718H10.9988L10.8832 5.04961ZM2.08119 6.71003C2.01396 6.83605 1.94913 6.96333 1.88674 7.09181L2.17119 7.30797L2.05739 6.93635L2.36043 6.71003H2.08119ZM3.87485 6.33244L3.75796 6.71001H3.38691L3.68726 6.93831L3.57609 7.29742L3.87051 7.07762L4.17357 7.30796L4.05977 6.93631L4.3628 6.71001H3.99048L3.87485 6.33244ZM5.87724 6.33244L5.76034 6.71001H5.38929L5.68964 6.93831L5.57847 7.29742L5.87289 7.07762L6.17596 7.30796L6.06215 6.93631L6.36518 6.71001H5.99286L5.87724 6.33244ZM7.87962 6.33244L7.76273 6.71001H7.39168L7.69203 6.93831L7.58086 7.29742L7.87527 7.07762L8.17834 7.30796L8.06453 6.93631L8.36756 6.71001H7.99525L7.87962 6.33244ZM9.882 6.33244L9.76512 6.71001H9.39406L9.69442 6.93831L9.58325 7.29742L9.87766 7.07762L10.1807 7.30796L10.0669 6.93631L10.37 6.71001H9.99764L9.882 6.33244ZM11.8844 6.33244L11.7675 6.71001H11.3964L11.6968 6.93831L11.5856 7.29742L11.88 7.07762L12.1831 7.30796L12.0693 6.93631L12.3723 6.71001H12L11.8844 6.33244ZM2.87366 7.61522L2.75677 7.99279H2.38571L2.68607 8.22109L2.5749 8.5802L2.86931 8.3604L3.17238 8.59074L3.05858 8.21909L3.3616 7.99279H2.98928L2.87366 7.61522ZM4.87604 7.61522L4.75915 7.99279H4.3881L4.68845 8.22109L4.57728 8.5802L4.8717 8.3604L5.17477 8.59074L5.06096 8.21909L5.36399 7.99279H4.99167L4.87604 7.61522ZM6.87843 7.61522L6.76154 7.99279H6.39049L6.69084 8.22109L6.57967 8.5802L6.87408 8.3604L7.17715 8.59074L7.06334 8.21909L7.36637 7.99279H6.99406L6.87843 7.61522ZM8.88081 7.61522L8.76393 7.99279H8.39287L8.69323 8.22109L8.58206 8.5802L8.87647 8.3604L9.17953 8.59074L9.06573 8.21909L9.36876 7.99279H8.99644L8.88081 7.61522ZM10.8832 7.61522L10.7663 7.99279H10.3953L10.6956 8.22109L10.5844 8.5802L10.8789 8.3604L11.1819 8.59074L11.0681 8.21909L11.3711 7.99279H10.9988L10.8832 7.61522ZM1.87247 8.89804L1.75558 9.27562H1.38452L1.68488 9.50392L1.57371 9.86303L1.86812 9.64322L2.17119 9.87357L2.05738 9.50192L2.36041 9.27562H1.9881L1.87247 8.89804ZM3.87485 8.89804L3.75796 9.27562H3.38691L3.68726 9.50392L3.57609 9.86303L3.87051 9.64322L4.17357 9.87357L4.05977 9.50192L4.3628 9.27562H3.99048L3.87485 8.89804ZM5.87724 8.89804L5.76034 9.27562H5.38929L5.68964 9.50392L5.57847 9.86303L5.87289 9.64322L6.17596 9.87357L6.06215 9.50192L6.36518 9.27562H5.99286L5.87724 8.89804ZM7.87962 8.89804L7.76273 9.27562H7.39168L7.69203 9.50392L7.58086 9.86303L7.87527 9.64322L8.17834 9.87357L8.06453 9.50192L8.36756 9.27562H7.99525L7.87962 8.89804ZM9.882 8.89804L9.76512 9.27562H9.39406L9.69442 9.50392L9.58325 9.86303L9.87766 9.64322L10.1807 9.87357L10.0669 9.50192L10.37 9.27562H9.99764L9.882 8.89804ZM11.8844 8.89804L11.7675 9.27562H11.3964L11.6968 9.50392L11.5856 9.86303L11.88 9.64322L12.1831 9.87357L12.0693 9.50192L12.3723 9.27562H12L11.8844 8.89804ZM2.87366 10.1809L2.75677 10.5584H2.38571L2.68607 10.7867L2.5749 11.1459L2.86931 10.9261L3.17238 11.1564L3.05858 10.7847L3.3616 10.5584H2.98928L2.87366 10.1809ZM4.87604 10.1809L4.75915 10.5584H4.3881L4.68845 10.7867L4.57728 11.1459L4.8717 10.9261L5.17477 11.1564L5.06096 10.7847L5.36399 10.5584H4.99167L4.87604 10.1809ZM6.87843 10.1809L6.76154 10.5584H6.39049L6.69084 10.7867L6.57967 11.1459L6.87408 10.9261L7.17715 11.1564L7.06334 10.7847L7.36637 10.5584H6.99406L6.87843 10.1809ZM8.88081 10.1809L8.76393 10.5584H8.39287L8.69323 10.7867L8.58206 11.1459L8.87647 10.9261L9.17953 11.1564L9.06573 10.7847L9.36876 10.5584H8.99644L8.88081 10.1809ZM10.8832 10.1809L10.7663 10.5584H10.3953L10.6956 10.7867L10.5844 11.1459L10.8789 10.9261L11.1819 11.1564L11.0681 10.7847L11.3711 10.5584H10.9988L10.8832 10.1809ZM1.87247 11.4637L1.75558 11.8412H1.38452L1.68488 12.0695L1.57371 12.4286L1.86812 12.2088L2.17119 12.4392L2.05738 12.0675L2.36041 11.8412H1.9881L1.87247 11.4637ZM3.87485 11.4637L3.75796 11.8412H3.38691L3.68726 12.0695L3.57609 12.4286L3.87051 12.2088L4.17357 12.4392L4.05977 12.0675L4.3628 11.8412H3.99048L3.87485 11.4637ZM5.87724 11.4637L5.76034 11.8412H5.38929L5.68964 12.0695L5.57847 12.4286L5.87289 12.2088L6.17596 12.4392L6.06215 12.0675L6.36518 11.8412H5.99286L5.87724 11.4637ZM7.87962 11.4637L7.76273 11.8412H7.39168L7.69203 12.0695L7.58086 12.4286L7.87527 12.2088L8.17834 12.4392L8.06453 12.0675L8.36756 11.8412H7.99525L7.87962 11.4637ZM9.882 11.4637L9.76512 11.8412H9.39406L9.69442 12.0695L9.58325 12.4286L9.87766 12.2088L10.1807 12.4392L10.0669 12.0675L10.37 11.8412H9.99764L9.882 11.4637ZM11.8844 11.4637L11.7675 11.8412H11.3964L11.6968 12.0695L11.5856 12.4286L11.88 12.2088L12.1831 12.4392L12.0693 12.0675L12.3723 11.8412H12L11.8844 11.4637Z" fill="white"></path>
    </g>
    <defs>
      <clipPath id="clip0_8121_115">
        <rect width="24" height="24" fill="white"></rect>
      </clipPath>
    </defs>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" id="arrow_down">
    <path d="M4 6L8 10L12 6" stroke="#151D48" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" id="Bag 1">
    <mask id="mask0_8121_977" maskUnits="userSpaceOnUse" x="0" y="0" width="18" height="18">
      <rect x="0.359375" y="0.360107" width="17.28" height="17.28" fill="white"></rect>
    </mask>
    <g mask="url(#mask0_8121_977)">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7599 15.84C13.9676 15.84 15.6614 15.0426 15.1803 11.8331L14.6201 7.48342C14.3235 5.88195 13.3021 5.26904 12.4057 5.26904H5.07724C4.16776 5.26904 3.20557 5.92809 2.86287 7.48342L2.30268 11.8331C1.89408 14.6801 3.54168 15.84 5.74946 15.84H11.7599Z" stroke="#4AB58E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
      <path d="M5.65039 5.11094C5.65039 3.39297 7.04308 2.00027 8.76104 2.00027C9.58832 1.99677 10.3829 2.32295 10.9691 2.90669C11.5554 3.49043 11.8849 4.28365 11.8849 5.11094" stroke="#4AB58E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
      <path d="M6.62598 8.35352H6.65893" stroke="#4AB58E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
      <path d="M10.8242 8.35352H10.8572" stroke="#4AB58E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
    </g>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" id="dashboard">
    <mask id="mask0_8121_67" maskUnits="userSpaceOnUse" x="0" y="0" width="32" height="32">
      <rect width="32" height="32" fill="white"></rect>
    </mask>
    <g mask="url(#mask0_8121_67)">
      <path d="M13.6704 7.84579L14.0416 13.3657L14.2258 16.1401C14.2278 16.4255 14.2725 16.7089 14.3588 16.9813C14.5813 17.5101 15.1168 17.8461 15.6993 17.8227L24.5756 17.242C24.96 17.2357 25.3312 17.3795 25.6074 17.6417C25.8377 17.8603 25.9864 18.1461 26.0333 18.4536L26.049 18.6403C25.6817 23.7265 21.9461 27.9689 16.8704 29.064C11.7946 30.1591 6.58965 27.8457 4.08145 23.3799C3.35836 22.0824 2.90671 20.6564 2.75301 19.1853C2.68881 18.7499 2.66055 18.31 2.66848 17.87C2.66055 12.417 6.5438 7.70262 11.9796 6.5661C12.6339 6.46422 13.2752 6.81057 13.5376 7.40738C13.6054 7.54559 13.6502 7.69362 13.6704 7.84579Z" fill="white"></path>
      <path opacity="0.4" d="M29.3335 13.083L29.3242 13.1264L29.2973 13.1896L29.301 13.3631C29.2871 13.5929 29.1983 13.8139 29.0455 13.9926C28.8862 14.1786 28.6686 14.3053 28.429 14.3545L28.2829 14.3745L18.0418 15.0381C17.7011 15.0717 17.3619 14.9618 17.1087 14.7359C16.8975 14.5475 16.7626 14.2934 16.7245 14.0195L16.0371 3.79334C16.0251 3.75877 16.0251 3.72129 16.0371 3.6867C16.0465 3.40482 16.1706 3.13838 16.3817 2.9469C16.5926 2.75542 16.8731 2.65486 17.1602 2.66769C23.2401 2.82237 28.3499 7.19431 29.3335 13.083Z" fill="white"></path>
    </g>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="DiscIcon">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M16.6261 5.26532L13.3263 5.73673C12.8222 5.80875 12.4103 6.04904 12.1162 6.38114L4.41674 14.0806C3.6357 14.8616 3.63566 16.1279 4.41674 16.909L7.24517 19.7374C8.02625 20.5185 9.29255 20.5185 10.0736 19.7374L17.773 12.038C18.1051 11.7439 18.3454 11.332 18.4174 10.8279L18.8888 7.52803C19.0775 6.20815 17.946 5.07671 16.6261 5.26532ZM14.3162 9.83793C14.7067 10.2284 15.3399 10.2285 15.7305 9.83793C16.121 9.4474 16.1209 8.81421 15.7305 8.42371C15.34 8.03322 14.7068 8.03319 14.3162 8.42372C13.9257 8.81424 13.9257 9.44743 14.3162 9.83793Z" fill="white"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 26" id="dummy">
    <path d="M6.37428 2.62737C5.24218 3.78044 5.24218 5.6463 6.39524 6.79937L8.91102 9.31514C11.28 11.6842 11.28 15.5417 8.91102 17.9107L4.29876 13.2985C3.11425 12.1139 2.51675 10.5521 2.51675 9.00067C2.51675 7.44928 3.11425 5.8874 4.29876 4.70289L6.34283 2.65882C6.35331 2.64833 6.36379 2.63785 6.37428 2.62737Z" fill="#5D5FEF" stroke="white" stroke-width="2" stroke-miterlimit="10"></path>
    <path d="M8.38678 8.78992L6.39513 6.79826C5.24206 5.6452 5.23158 3.78981 6.37416 2.62627C7.50626 1.51513 9.30923 1.5361 10.4309 2.65771C10.9969 3.22376 11.2799 3.96801 11.2799 4.70178C11.2799 5.43555 10.9969 6.1798 10.4309 6.74585L9.95914 7.21756" fill="#5D5FEF"></path>
    <path d="M8.38678 8.78992L6.39513 6.79826C5.24206 5.6452 5.23158 3.78981 6.37416 2.62627C7.50626 1.51513 9.30923 1.5361 10.4309 2.65771C10.9969 3.22376 11.2799 3.96801 11.2799 4.70178C11.2799 5.43555 10.9969 6.1798 10.4309 6.74585L9.95914 7.21756" stroke="white" stroke-width="2" stroke-miterlimit="10"></path>
    <path d="M18.2715 23.2246C19.4036 22.0715 19.4036 20.2056 18.2505 19.0526L15.7348 16.5368C13.3657 14.1678 13.3657 10.3102 15.7348 7.94121L20.347 12.5535C21.5315 13.738 22.129 15.2999 22.129 16.8513C22.129 18.4027 21.5315 19.9645 20.347 21.149L18.3029 23.1931C18.282 23.2141 18.2715 23.2246 18.2715 23.2246Z" fill="#5D5FEF" stroke="white" stroke-width="2" stroke-miterlimit="10"></path>
    <path d="M16.2479 17.0713L18.2396 19.063C19.3926 20.2161 19.4031 22.0715 18.2606 23.235C17.1285 24.3461 15.3255 24.3252 14.2039 23.2036C13.6378 22.6375 13.3548 21.8933 13.3548 21.1595C13.3548 20.4257 13.6378 19.6815 14.2039 19.1154L14.686 18.6332" fill="#5D5FEF"></path>
    <path d="M16.2479 17.0713L18.2396 19.063C19.3926 20.2161 19.4031 22.0715 18.2606 23.235C17.1285 24.3461 15.3255 24.3252 14.2039 23.2036C13.6378 22.6375 13.3548 21.8933 13.3548 21.1595C13.3548 20.4257 13.6378 19.6815 14.2039 19.1154L14.686 18.6332" stroke="white" stroke-width="2" stroke-miterlimit="10"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" id="leader_board">
    <path d="M4 16H6.66667V28H4V16ZM25.3333 10.6667H28V28H25.3333V10.6667ZM14.6667 2.66666H17.3333V28H14.6667V2.66666Z" fill="#737791"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" id="Export">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1007 5.11257L8.43899 2.83138C8.31472 2.72244 8.15784 2.66671 8.00013 2.66663C7.89223 2.66657 7.78393 2.69257 7.68544 2.74541C7.64154 2.76889 7.59984 2.79757 7.56128 2.83138L4.89959 5.11257C4.62003 5.35217 4.58763 5.77303 4.82723 6.0526C5.06683 6.33216 5.4877 6.36456 5.76726 6.12496L7.33342 4.78268V9.94578C7.33342 10.314 7.6319 10.6124 8.00009 10.6124C8.36828 10.6124 8.66676 10.314 8.66676 9.94578L8.66676 4.78261L10.233 6.12496C10.5126 6.36456 10.9334 6.33216 11.173 6.0526C11.4126 5.77303 11.3802 5.35217 11.1007 5.11257ZM4.00008 9.33329C4.00008 8.9651 4.29856 8.66663 4.66675 8.66663H5.66675C6.03494 8.66663 6.33341 8.36815 6.33341 7.99996C6.33341 7.63177 6.03494 7.33329 5.66675 7.33329H4.66675C3.56218 7.33329 2.66675 8.22872 2.66675 9.33329V11.3333C2.66675 12.4379 3.56218 13.3333 4.66675 13.3333H11.3334C12.438 13.3333 13.3334 12.4379 13.3334 11.3333V9.33329C13.3334 8.22872 12.438 7.33329 11.3334 7.33329H10.3334C9.96522 7.33329 9.66675 7.63177 9.66675 7.99996C9.66675 8.36815 9.96522 8.66663 10.3334 8.66663H11.3334C11.7016 8.66663 12.0001 8.9651 12.0001 9.33329V11.3333C12.0001 11.7015 11.7016 12 11.3334 12H4.66675C4.29856 12 4.00008 11.7015 4.00008 11.3333V9.33329Z" fill="#0F3659"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 26" id="logo">
    <path d="M6.37428 2.62737C5.24218 3.78044 5.24218 5.6463 6.39524 6.79937L8.91102 9.31514C11.28 11.6842 11.28 15.5417 8.91102 17.9107L4.29876 13.2985C3.11425 12.1139 2.51675 10.5521 2.51675 9.00067C2.51675 7.44928 3.11425 5.8874 4.29876 4.70289L6.34283 2.65882C6.35331 2.64833 6.36379 2.63785 6.37428 2.62737Z" fill="#5D5FEF" stroke="white" stroke-width="2" stroke-miterlimit="10"></path>
    <path d="M8.38678 8.78992L6.39513 6.79826C5.24206 5.6452 5.23158 3.78981 6.37416 2.62627C7.50626 1.51513 9.30923 1.5361 10.4309 2.65771C10.9969 3.22376 11.2799 3.96801 11.2799 4.70178C11.2799 5.43555 10.9969 6.1798 10.4309 6.74585L9.95914 7.21756" fill="#5D5FEF"></path>
    <path d="M8.38678 8.78992L6.39513 6.79826C5.24206 5.6452 5.23158 3.78981 6.37416 2.62627C7.50626 1.51513 9.30923 1.5361 10.4309 2.65771C10.9969 3.22376 11.2799 3.96801 11.2799 4.70178C11.2799 5.43555 10.9969 6.1798 10.4309 6.74585L9.95914 7.21756" stroke="white" stroke-width="2" stroke-miterlimit="10"></path>
    <path d="M18.2715 23.2246C19.4036 22.0715 19.4036 20.2056 18.2505 19.0526L15.7348 16.5368C13.3657 14.1678 13.3657 10.3102 15.7348 7.94121L20.347 12.5535C21.5315 13.738 22.129 15.2999 22.129 16.8513C22.129 18.4027 21.5315 19.9645 20.347 21.149L18.3029 23.1931C18.282 23.2141 18.2715 23.2246 18.2715 23.2246Z" fill="#5D5FEF" stroke="white" stroke-width="2" stroke-miterlimit="10"></path>
    <path d="M16.2479 17.0713L18.2396 19.063C19.3926 20.2161 19.4031 22.0715 18.2606 23.235C17.1285 24.3461 15.3255 24.3252 14.2039 23.2036C13.6378 22.6375 13.3548 21.8933 13.3548 21.1595C13.3548 20.4257 13.6378 19.6815 14.2039 19.1154L14.686 18.6332" fill="#5D5FEF"></path>
    <path d="M16.2479 17.0713L18.2396 19.063C19.3926 20.2161 19.4031 22.0715 18.2606 23.235C17.1285 24.3461 15.3255 24.3252 14.2039 23.2036C13.6378 22.6375 13.3548 21.8933 13.3548 21.1595C13.3548 20.4257 13.6378 19.6815 14.2039 19.1154L14.686 18.6332" stroke="white" stroke-width="2" stroke-miterlimit="10"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" id="magnifier">
    <path d="M27.414 24.586L22.337 19.509C23.386 17.928 24 16.035 24 14C24 8.486 19.514 4 14 4C8.486 4 4 8.486 4 14C4 19.514 8.486 24 14 24C16.035 24 17.928 23.386 19.509 22.337L24.586 27.414C25.366 28.195 26.634 28.195 27.414 27.414C28.195 26.633 28.195 25.367 27.414 24.586ZM7 14C7 10.14 10.14 7 14 7C17.86 7 21 10.14 21 14C21 17.86 17.86 21 14 21C10.14 21 7 17.86 7 14Z" fill="#5D5FEF"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" id="mdi_cog-outline">
    <path d="M16 10.6667C17.4145 10.6667 18.7711 11.2286 19.7713 12.2288C20.7714 13.229 21.3334 14.5855 21.3334 16C21.3334 17.4145 20.7714 18.771 19.7713 19.7712C18.7711 20.7714 17.4145 21.3333 16 21.3333C14.5855 21.3333 13.229 20.7714 12.2288 19.7712C11.2286 18.771 10.6667 17.4145 10.6667 16C10.6667 14.5855 11.2286 13.229 12.2288 12.2288C13.229 11.2286 14.5855 10.6667 16 10.6667ZM16 13.3333C15.2928 13.3333 14.6145 13.6143 14.1144 14.1144C13.6143 14.6145 13.3334 15.2928 13.3334 16C13.3334 16.7072 13.6143 17.3855 14.1144 17.8856C14.6145 18.3857 15.2928 18.6667 16 18.6667C16.7073 18.6667 17.3855 18.3857 17.8856 17.8856C18.3857 17.3855 18.6667 16.7072 18.6667 16C18.6667 15.2928 18.3857 14.6145 17.8856 14.1144C17.3855 13.6143 16.7073 13.3333 16 13.3333ZM13.3334 29.3333C13 29.3333 12.72 29.0933 12.6667 28.7733L12.1734 25.24C11.3334 24.9067 10.6134 24.4533 9.92002 23.92L6.60002 25.2667C6.30669 25.3733 5.94669 25.2667 5.78669 24.9733L3.12002 20.36C3.03841 20.2226 3.00964 20.0602 3.03909 19.9032C3.06854 19.7461 3.15419 19.6052 3.28002 19.5067L6.09335 17.2933L6.00002 16L6.09335 14.6667L3.28002 12.4933C3.15419 12.3948 3.06854 12.2539 3.03909 12.0968C3.00964 11.9398 3.03841 11.7774 3.12002 11.64L5.78669 7.02667C5.94669 6.73333 6.30669 6.61333 6.60002 6.73333L9.92002 8.06667C10.6134 7.54667 11.3334 7.09333 12.1734 6.76L12.6667 3.22667C12.72 2.90667 13 2.66667 13.3334 2.66667H18.6667C19 2.66667 19.28 2.90667 19.3334 3.22667L19.8267 6.76C20.6667 7.09333 21.3867 7.54667 22.08 8.06667L25.4 6.73333C25.6934 6.61333 26.0534 6.73333 26.2134 7.02667L28.88 11.64C29.0534 11.9333 28.9734 12.2933 28.72 12.4933L25.9067 14.6667L26 16L25.9067 17.3333L28.72 19.5067C28.9734 19.7067 29.0534 20.0667 28.88 20.36L26.2134 24.9733C26.0534 25.2667 25.6934 25.3867 25.4 25.2667L22.08 23.9333C21.3867 24.4533 20.6667 24.9067 19.8267 25.24L19.3334 28.7733C19.28 29.0933 19 29.3333 18.6667 29.3333H13.3334ZM15 5.33333L14.5067 8.81333C12.9067 9.14667 11.4934 10 10.4667 11.1867L7.25335 9.8L6.25335 11.5333L9.06669 13.6C8.53335 15.1556 8.53335 16.8444 9.06669 18.4L6.24002 20.48L7.24002 22.2133L10.48 20.8267C11.5067 22 12.9067 22.8533 14.4934 23.1733L14.9867 26.6667H17.0134L17.5067 23.1867C19.0934 22.8533 20.4934 22 21.52 20.8267L24.76 22.2133L25.76 20.48L22.9334 18.4133C23.4667 16.8533 23.4667 15.16 22.9334 13.6L25.7467 11.5333L24.7467 9.8L21.5333 11.1867C20.4856 9.97376 19.0645 9.14357 17.4934 8.82667L17 5.33333H15Z" fill="#737791"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" id="mdi_message-processing-outline">
    <path d="M26.6667 2.66666H5.33335C3.86669 2.66666 2.66669 3.86666 2.66669 5.33333V29.3333L8.00002 24H26.6667C28.1334 24 29.3334 22.8 29.3334 21.3333V5.33333C29.3334 3.86666 28.1334 2.66666 26.6667 2.66666ZM26.6667 21.3333H6.93335L5.33335 22.9333V5.33333H26.6667V21.3333ZM22.6667 14.6667H20V12H22.6667V14.6667ZM17.3334 14.6667H14.6667V12H17.3334V14.6667ZM12 14.6667H9.33335V12H12" fill="#737791"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" id="mdi_shopping-outline">
    <path d="M25.3333 8H22.6667C22.6667 4.26666 19.7333 1.33333 16 1.33333C12.2667 1.33333 9.33333 4.26666 9.33333 8H6.66667C5.2 8 4 9.2 4 10.6667V26.6667C4 28.1333 5.2 29.3333 6.66667 29.3333H25.3333C26.8 29.3333 28 28.1333 28 26.6667V10.6667C28 9.2 26.8 8 25.3333 8ZM16 3.99999C18.2667 3.99999 20 5.73333 20 8H12C12 5.73333 13.7333 3.99999 16 3.99999ZM25.3333 26.6667H6.66667V10.6667H25.3333V26.6667ZM16 16C13.7333 16 12 14.2667 12 12H9.33333C9.33333 15.7333 12.2667 18.6667 16 18.6667C19.7333 18.6667 22.6667 15.7333 22.6667 12H20C20 14.2667 18.2667 16 16 16Z" fill="#737791"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="New Costumers Icon">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M14 8C14 10.2091 12.2091 12 10 12C7.79086 12 6 10.2091 6 8C6 5.79086 7.79086 4 10 4C12.2091 4 14 5.79086 14 8ZM10 13C6.13401 13 3 15.2386 3 18C3 19.1046 3.89543 20 5 20H15C16.1046 20 17 19.1046 17 18C17 15.2386 13.866 13 10 13ZM18 6C18.5523 6 19 6.44772 19 7V8H20C20.5523 8 21 8.44772 21 9C21 9.55228 20.5523 10 20 10H19V11C19 11.5523 18.5523 12 18 12C17.4477 12 17 11.5523 17 11V10H16C15.4477 10 15 9.55228 15 9C15 8.44771 15.4477 8 16 8H17V7C17 6.44772 17.4477 6 18 6Z" fill="white"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="Order Icon">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M4 6C4 3.79086 5.79086 2 8 2H14V6C14 8.20914 15.7909 10 18 10H20V18C20 20.2091 18.2091 22 16 22H8C5.79086 22 4 20.2091 4 18V6ZM8 11C7.44772 11 7 11.4477 7 12C7 12.5523 7.44772 13 8 13H10C10.5523 13 11 12.5523 11 12C11 11.4477 10.5523 11 10 11H8ZM8 15C7.44772 15 7 15.4477 7 16C7 16.5523 7.44772 17 8 17H12C12.5523 17 13 16.5523 13 16C13 15.4477 12.5523 15 12 15H8ZM16.6818 4.19879L16.5509 6.16288C16.5106 6.76656 17.0115 7.26743 17.6152 7.22718L19.5792 7.09624C20.4365 7.03909 20.8274 5.99887 20.2198 5.39135L18.3867 3.5582C17.7792 2.95068 16.7389 3.34153 16.6818 4.19879Z" fill="white"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" id="order">
    <path d="M4 4H6.66667L7.2 6.66667M9.33333 17.3333H22.6667L28 6.66667H7.2M9.33333 17.3333L7.2 6.66667M9.33333 17.3333L6.27614 20.3905C5.43619 21.2305 6.03108 22.6667 7.21895 22.6667H22.6667M22.6667 22.6667C21.1939 22.6667 20 23.8606 20 25.3333C20 26.8061 21.1939 28 22.6667 28C24.1394 28 25.3333 26.8061 25.3333 25.3333C25.3333 23.8606 24.1394 22.6667 22.6667 22.6667ZM12 25.3333C12 26.8061 10.8061 28 9.33333 28C7.86057 28 6.66667 26.8061 6.66667 25.3333C6.66667 23.8606 7.86057 22.6667 9.33333 22.6667C10.8061 22.6667 12 23.8606 12 25.3333Z" stroke="#737791" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="OrderIcon">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M4 6C4 3.79086 5.79086 2 8 2H14V6C14 8.20914 15.7909 10 18 10H20V18C20 20.2091 18.2091 22 16 22H8C5.79086 22 4 20.2091 4 18V6ZM8 11C7.44772 11 7 11.4477 7 12C7 12.5523 7.44772 13 8 13H10C10.5523 13 11 12.5523 11 12C11 11.4477 10.5523 11 10 11H8ZM8 15C7.44772 15 7 15.4477 7 16C7 16.5523 7.44772 17 8 17H12C12.5523 17 13 16.5523 13 16C13 15.4477 12.5523 15 12 15H8ZM16.6818 4.19879L16.5509 6.16288C16.5106 6.76656 17.0115 7.26743 17.6152 7.22718L19.5792 7.09624C20.4365 7.03909 20.8274 5.99887 20.2198 5.39135L18.3867 3.5582C17.7792 2.95068 16.7389 3.34153 16.6818 4.19879Z" fill="white"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" id="sales_report">
    <path d="M28 26.6667H4V6.66667" stroke="#737791" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
    <path d="M28 9.33333L17.3333 18.6667L12 13.3333L4 20" stroke="#737791" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="SalesIcon">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M5 3C3.89543 3 3 3.89545 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89545 20.1046 3 19 3H5ZM8 13C8 12.4477 7.55228 12 7 12C6.44772 12 6 12.4477 6 13V17C6 17.5523 6.44772 18 7 18C7.55228 18 8 17.5523 8 17V13ZM12 9C12.5523 9 13 9.44769 13 10V17C13 17.5523 12.5523 18 12 18C11.4477 18 11 17.5523 11 17V10C11 9.44769 11.4477 9 12 9ZM18 7C18 6.44769 17.5523 6 17 6C16.4477 6 16 6.44769 16 7V17C16 17.5523 16.4477 18 17 18C17.5523 18 18 17.5523 18 17V7Z" fill="white"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="sales">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M5 3C3.89543 3 3 3.89545 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89545 20.1046 3 19 3H5ZM8 13C8 12.4477 7.55228 12 7 12C6.44772 12 6 12.4477 6 13V17C6 17.5523 6.44772 18 7 18C7.55228 18 8 17.5523 8 17V13ZM12 9C12.5523 9 13 9.44769 13 10V17C13 17.5523 12.5523 18 12 18C11.4477 18 11 17.5523 11 17V10C11 9.44769 11.4477 9 12 9ZM18 7C18 6.44769 17.5523 6 17 6C16.4477 6 16 6.44769 16 7V17C16 17.5523 16.4477 18 17 18C17.5523 18 18 17.5523 18 17V7Z" fill="white"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" id="Sign Out Icon">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M21.7747 22.2011L26.3371 16.8777C26.555 16.6292 26.6665 16.3154 26.6666 16C26.6667 15.7842 26.6147 15.5676 26.5091 15.3706C26.4621 15.2828 26.4047 15.1994 26.3371 15.1223L21.7747 9.79894C21.2955 9.23982 20.4538 9.17502 19.8947 9.65422C19.3356 10.1334 19.2708 10.9751 19.75 11.5343L22.4345 14.6666L12.1083 14.6666C11.3719 14.6666 10.775 15.2636 10.775 15.9999C10.775 16.7363 11.3719 17.3333 12.1083 17.3333L22.4347 17.3333L19.75 20.4658C19.2708 21.0249 19.3356 21.8666 19.8947 22.3458C20.4538 22.825 21.2955 22.7602 21.7747 22.2011ZM13.3333 7.99992C14.0697 7.99992 14.6666 8.59687 14.6666 9.33325L14.6666 11.3333C14.6666 12.0696 15.2636 12.6666 16 12.6666C16.7363 12.6666 17.3333 12.0696 17.3333 11.3333L17.3333 9.33325C17.3333 7.12411 15.5424 5.33325 13.3333 5.33325L9.33329 5.33325C7.12415 5.33325 5.33329 7.12411 5.33329 9.33325L5.33329 22.6666C5.33329 24.8757 7.12415 26.6666 9.33329 26.6666L13.3333 26.6666C15.5424 26.6666 17.3333 24.8757 17.3333 22.6666L17.3333 20.6666C17.3333 19.9302 16.7363 19.3333 16 19.3333C15.2636 19.3333 14.6666 19.9302 14.6666 20.6666L14.6666 22.6666C14.6666 23.403 14.0697 23.9999 13.3333 23.9999L9.33329 23.9999C8.59691 23.9999 7.99996 23.403 7.99996 22.6666L7.99996 9.33325C7.99996 8.59687 8.59691 7.99992 9.33329 7.99992L13.3333 7.99992Z" fill="#737791"></path>
  </symbol>
  <symbol fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" id="Ticket Star 1">
    <mask id="mask0_8121_991" maskUnits="userSpaceOnUse" x="0" y="0" width="18" height="18">
      <rect x="0.360352" y="0.360107" width="17.28" height="17.28" fill="white"></rect>
    </mask>
    <g mask="url(#mask0_8121_991)">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M1.8015 5.50279C1.80161 3.85536 3.13701 2.52002 4.78446 2.52002H13.2171C14.8647 2.52002 16.1991 3.85546 16.1993 5.50258M16.1993 5.50258L16.2008 7.44874C16.2009 7.59203 16.144 7.72948 16.0428 7.83084C15.9415 7.93219 15.8041 7.98914 15.6608 7.98914C15.0659 7.98914 14.588 8.46697 14.588 9.06123C14.588 9.65492 15.0661 10.1333 15.6608 10.1333C15.959 10.1333 16.2008 10.3751 16.2008 10.6733V12.6173C16.2008 14.2647 14.8662 15.6003 13.2178 15.6003H4.78302C3.1353 15.6003 1.80078 14.2646 1.80078 12.6173V10.6733C1.80078 10.3751 2.04255 10.1333 2.34078 10.1333C2.93463 10.1333 3.41286 9.65507 3.41286 9.06123C3.41286 8.49452 2.96328 8.05178 2.34078 8.05178C2.19753 8.05178 2.06015 7.99486 1.95888 7.89355C1.8576 7.79224 1.80073 7.65484 1.80078 7.51159L1.8015 5.50279C1.8015 5.50272 1.8015 5.50285 1.8015 5.50279M2.8815 5.50298L2.88095 7.03627C3.79355 7.25875 4.49286 8.03701 4.49286 9.06123C4.49286 10.065 3.80894 10.9062 2.88078 11.1453V12.6173C2.88078 13.6686 3.73218 14.5203 4.78302 14.5203H13.2178C14.2695 14.5203 15.1208 13.6685 15.1208 12.6173V11.1453C14.1921 10.9064 13.508 10.0652 13.508 9.06123C13.508 8.05666 14.192 7.21605 15.1204 6.97717L15.1193 5.50338C15.1193 4.45213 14.2679 3.60002 13.2171 3.60002H4.78446C3.73342 3.60002 2.8815 4.45193 2.8815 5.50298ZM1.8015 5.50279L16.1993 5.50258L1.8015 5.50279Z" fill="#FFA800"></path>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M9.62802 6.45754L10.129 7.47206L11.2508 7.6352C11.8212 7.71982 12.0536 8.42185 11.6375 8.82844L11.6367 8.82926L10.8259 9.61813L11.0176 10.734C11.1162 11.31 10.5117 11.7379 10.0031 11.4727L10.0015 11.4719L9 10.9452L7.99689 11.4727C7.48832 11.7379 6.88375 11.3103 6.9824 10.7343L7.1741 9.61801L6.36393 8.82912L6.36323 8.82844C5.94718 8.42184 6.17957 7.71982 6.74997 7.6352L6.75146 7.63498L7.87104 7.47208L8.37253 6.45643L8.37315 6.45518C8.62942 5.93951 9.37013 5.93357 9.62768 6.45683C9.62779 6.45707 9.62791 6.45731 9.62802 6.45754ZM8.7138 8.20467C8.63514 8.36397 8.48316 8.47439 8.30736 8.49997L7.66933 8.5928L8.13113 9.04247C8.25856 9.16656 8.31672 9.34545 8.28661 9.52075L8.17723 10.1577L8.74867 9.85717C8.90601 9.77443 9.094 9.77443 9.25134 9.85717L9.82278 10.1577L9.71339 9.52076C9.68328 9.34538 9.74149 9.16643 9.86902 9.04233L10.331 8.5928L9.69269 8.49997C9.51687 8.4744 9.36487 8.36398 9.28621 8.20467L9 7.62502L8.7138 8.20467Z" fill="#FFA800"></path>
    </g>
  </symbol>
</svg>
    <aside role="navigation" aria-label="Main navigation">
        <div class="brand">
            <img src="./assets/images/dummy.svg" alt="Dabang company logo" width="32" height="32" loading="eager" />
            <span>Dabang</span>
        </div>
        <nav aria-label="Primary navigation">
            <ul role="list">
                <li>
                    <a href="#dashboard" class="active" aria-current="page" role="menuitem">
                        <img src="./assets/images/dashboard.svg" alt="" width="24" height="24" loading="lazy" aria-hidden="true" />
                        <span>Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="#leaderboard" role="menuitem">
                        <img src="./assets/images/leader_board.svg" alt="" width="24" height="24" loading="lazy" aria-hidden="true" />
                        <span>Leaderboard</span>
                    </a>
                </li>
                <li>
                    <a href="#orders" role="menuitem">
                        <img src="./assets/images/order.svg" alt="" width="24" height="24" loading="lazy" aria-hidden="true" />
                        <span>Orders</span>
                    </a>
                </li>
                <li>
                    <a href="#products" role="menuitem">
                        <img src="./assets/images/mdi_shopping-outline.svg" alt="" width="24" height="24" loading="lazy" aria-hidden="true" />
                        <span>Products</span>
                    </a>
                </li>
                <li>
                    <a href="#sales-report" role="menuitem">
                        <img src="./assets/images/sales_report.svg" alt="" width="24" height="24" loading="lazy" aria-hidden="true" />
                        <span>Sales Report</span>
                    </a>
                </li>
                <li>
                    <a href="#messages" role="menuitem">
                        <img src="./assets/images/mdi_message-processing-outline.svg" alt="" width="24" height="24" loading="lazy" aria-hidden="true" />
                        <span>Messages</span>
                    </a>
                </li>
                <li>
                    <a href="#settings" role="menuitem">
                        <img src="./assets/images/mdi_cog-outline.svg" alt="" width="24" height="24" loading="lazy" aria-hidden="true" />
                        <span>Settings</span>
                    </a>
                </li>
                <li>
                    <button type="button" class="sign-out-btn" aria-label="Sign out of admin dashboard">
                        <img src="./assets/images/Sign Out Icon.svg" alt="" width="24" height="24" loading="lazy" aria-hidden="true" />
                        <span>Sign Out</span>
                    </button>
                </li>
            </ul>
        </nav>
    </aside>

    <main id="main-content" role="main">
        <header class="page-header" role="banner">
            <h1>Dashboard</h1>
            <div class="header-right">
                <div class="search-bar-container" role="search">
                    <input
                        type="search"
                        id="search-input"
                        name="search"
                        placeholder="Search here"
                        autocomplete="off"
                        aria-label="Search dashboard content"
                    />
                    <img src="./assets/images/magnifier.svg" alt="" width="20" height="20" loading="lazy" aria-hidden="true" />
                </div>

                <div class="language-selector">
                    <label for="language-select" class="visually-hidden">Select language</label>
                    <select id="language-select" name="language" aria-label="Language selection">
                        <option value="en-US">ENG (US)</option>
                        <option value="es-ES">ESP (ES)</option>
                        <option value="fr-FR">FRA (FR)</option>
                    </select>
                </div>

                <div class="profile-menu" role="button" tabindex="0" aria-haspopup="true" aria-expanded="false" aria-label="User profile menu">
                    <img src="./assets/images/profile.png" alt="Vijaya's profile picture" width="40" height="40" loading="lazy" />
                    <div class="profile-info">
                        <span class="profile-name">Vijay</span>
                        <span class="profile-role">Admin</span>
                    </div>
                    <img src="./assets/images/arrow_down.svg" alt="" width="16" height="16" loading="lazy" aria-hidden="true" />
                </div>
            </div>
        </header>

        <section class="dashboard" aria-label="Dashboard content">
            <div class="dashboard-grid">
                <section class="sales-section" aria-labelledby="sales-heading">
                    <header class="section-header">
                        <div class="section-header-left">
                            <h2 id="sales-heading">Today's Sales</h2>
                            <p class="section-description">Sales summary</p>
                        </div>
                        <button type="button" class="export-btn" aria-label="Export sales data">
                            <img src="./assets/images/export.svg" alt="" width="20" height="20" loading="lazy" aria-hidden="true" />
                            <span>Export</span>
                        </button>
                    </header>
                    <div class="sales-cards" role="list" aria-label="Sales metrics">
                        <article class="sales-card" role="listitem">
                            <img src="./assets/images/SalesIcon.svg" alt="" width="48" height="48" loading="lazy" aria-hidden="true" />
                            <div class="sales-card-content">
                                <data value="1000" class="metric-value">$1k</data>
                                <h3 class="metric-label">Total Sales</h3>
                            </div>
                            <p class="metric-change positive" aria-label="8% increase from yesterday">+8% from yesterday</p>
                        </article>
                        <article class="sales-card" role="listitem">
                            <img src="./assets/images/OrderIcon.svg" alt="" width="48" height="48" loading="lazy" aria-hidden="true" />
                            <div class="sales-card-content">
                                <data value="300" class="metric-value">300</data>
                                <h3 class="metric-label">Total Orders</h3>
                            </div>
                            <p class="metric-change positive" aria-label="8% increase from yesterday">+8% from yesterday</p>
                        </article>
                        <article class="sales-card" role="listitem">
                            <img src="./assets/images/DiscIcon.svg" alt="" width="48" height="48" loading="lazy" aria-hidden="true" />
                            <div class="sales-card-content">
                                <data value="5" class="metric-value">5</data>
                                <h3 class="metric-label">Products Sold</h3>
                            </div>
                            <p class="metric-change positive" aria-label="8% increase from yesterday">+8% from yesterday</p>
                        </article>
                        <article class="sales-card" role="listitem">
                            <img src="./assets/images/New%20Costumers%20Icon.svg" alt="" width="48" height="48" loading="lazy" aria-hidden="true" />
                            <div class="sales-card-content">
                                <data value="8" class="metric-value">8</data>
                                <h3 class="metric-label">New Customers</h3>
                            </div>
                            <p class="metric-change positive" aria-label="8% increase from yesterday">+8% from yesterday</p>
                        </article>
                    </div>
                </section>
                <section class="chart-card" aria-labelledby="visitor-insights-heading">
                    <h2 id="visitor-insights-heading">Visitor Insights</h2>
                    <div id="container" role="img" aria-label="Visitor insights chart showing customer trends"></div>
                </section>
            </div>
            <div class="dashboard-grid dashboard-grid-secondary">
                <section class="chart-card" aria-labelledby="revenue-heading">
                    <h2 id="revenue-heading">Total Revenue</h2>
                    <div id="container2" role="img" aria-label="Total revenue chart showing weekly performance"></div>
                </section>
                <section class="chart-card" aria-labelledby="satisfaction-heading">
                    <h2 id="satisfaction-heading">Customer Satisfaction</h2>
                    <div id="container3" role="img" aria-label="Customer satisfaction metrics over time"></div>
                </section>
                <section class="chart-card" aria-labelledby="sales-comparison-heading">
                    <h2 id="sales-comparison-heading" class="visually-hidden">Sales Comparison</h2>
                  
                    <div id="container4" role="img" aria-label="Sales comparison chart between reality and target sales"></div>
                      <div class="custom-legend" role="list" aria-label="Sales metrics legend">
                        <div class="legend-item" data-series="0" role="listitem">
                            <div class="legend-left">
                                <img src="./assets/images/Bag 1.svg" alt="" width="24" height="24" loading="lazy" aria-hidden="true" />
                                <div class="legend-text">
                                    <span class="legend-label">Reality Sales</span>
                                    <strong class="legend-category">Global</strong>
                                </div>
                            </div>
                            <data value="9888" class="legend-value">9,888</data>
                        </div>
                        <div class="legend-item" data-series="1" role="listitem">
                            <div class="legend-left">
                                <img src="./assets/images/Ticket Star 1.svg" alt="" width="24" height="24" loading="lazy" aria-hidden="true" />
                                <div class="legend-text">
                                    <span class="legend-label">Target Sales</span>
                                    <strong class="legend-category">Commercial</strong>
                                </div>
                            </div>
                            <data value="9888" class="legend-value">9,888</data>
                        </div>
                    </div>              
                </section>
            </div>
            <div class="dashboard-grid dashboard-grid-tertiary">
                <section class="table_section chart-card" aria-labelledby="top-products-heading">
                    <h2 id="top-products-heading">Top Products</h2>
                    <div class="table-container" role="region" aria-label="Top products table" tabindex="0">
                        <table role="table" aria-label="Top selling products with popularity and sales data">
                            <thead>
                                <tr role="row">
                                    <th scope="col" role="columnheader">#</th>
                                    <th scope="col" role="columnheader">Product Name</th>
                                    <th scope="col" role="columnheader">Popularity</th>
                                    <th scope="col" role="columnheader">Sales</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr role="row">
                                    <td role="cell">1</td>
                                    <td role="cell">Apple Smartwatches</td>
                                    <td role="cell">
                                        <progress value="45" max="100" aria-label="45% popularity">45%</progress>
                                    </td>
                                    <td role="cell">
                                        <span class="chip" aria-label="45% sales performance">45%</span>
                                    </td>
                                </tr>
                                <tr role="row">
                                    <td role="cell">2</td>
                                    <td role="cell">Samsung Earbuds</td>
                                    <td role="cell">
                                        <progress value="50" max="100" aria-label="50% popularity">50%</progress>
                                    </td>
                                    <td role="cell">
                                        <span class="chip" aria-label="50% sales performance">50%</span>
                                    </td>
                                </tr>
                                <tr role="row">
                                    <td role="cell">3</td>
                                    <td role="cell">Fitbit Bands</td>
                                    <td role="cell">
                                        <progress value="90" max="100" aria-label="90% popularity">90%</progress>
                                    </td>
                                    <td role="cell">
                                        <span class="chip" aria-label="90% sales performance">90%</span>
                                    </td>
                                </tr>
                                <tr role="row">
                                    <td role="cell">4</td>
                                    <td role="cell">Google Nest</td>
                                    <td role="cell">
                                        <progress value="60" max="100" aria-label="60% popularity">60%</progress>
                                    </td>
                                    <td role="cell">
                                        <span class="chip" aria-label="60% sales performance">60%</span>
                                    </td>
                                </tr>
                                <tr role="row">
                                    <td role="cell">5</td>
                                    <td role="cell">OnePlus Tablets</td>
                                    <td role="cell">
                                        <progress value="40" max="100" aria-label="40% popularity">40%</progress>
                                    </td>
                                    <td role="cell">
                                        <span class="chip" aria-label="40% sales performance">40%</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>
                <section class="chart-card" aria-labelledby="sales-mapping-heading">
                    <h2 id="sales-mapping-heading">Sales Mapping by Country</h2>
                    <div id="container5" role="img" aria-label="World map showing sales distribution by country"></div>
                </section>
                <section class="chart-card" aria-labelledby="volume-service-heading">
                    <h2 id="volume-service-heading">Volume vs Service Level</h2>
                    <div id="container6" role="img" aria-label="Scatter plot showing relationship between volume and service level"></div>
                </section>
            </div>
        </section>
        
    </main>

    <!-- Error boundary for JavaScript errors -->
    <div id="error-boundary" class="error-boundary" role="alert" aria-live="assertive" style="display: none;">
        <h2>Something went wrong</h2>
        <p>Please refresh the page or contact support if the problem persists.</p>
        <button type="button" onclick="location.reload()">Refresh Page</button>
    </div>




 <script src="https://code.highcharts.com/maps/highmaps.js"></script>
    <script src="https://code.highcharts.com/maps/modules/exporting.js"></script>
    <script src="https://code.highcharts.com/mapdata/custom/world.js"></script>
    <!-- Our script loaded after Highcharts -->
    <script src="script.js"></script>


</body>

</html>