// Service Worker for Dabang Admin Dashboard
const CACHE_NAME = 'dabang-admin-v1';
const urlsToCache = [
  '/',
  '/admin.html',
  '/style.css',
  '/script.js',
  '/assets/images/dummy.svg',
  '/assets/images/dashboard.svg',
  '/assets/images/leader_board.svg',
  '/assets/images/order.svg',
  '/assets/images/mdi_shopping-outline.svg',
  '/assets/images/sales_report.svg',
  '/assets/images/mdi_message-processing-outline.svg',
  '/assets/images/mdi_cog-outline.svg',
  '/assets/images/Sign Out Icon.svg',
  '/assets/images/magnifier.svg',
  '/assets/images/profile.png',
  '/assets/images/arrow_down.svg',
  '/assets/images/export.svg'
];

// Install event - cache resources
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      }
    )
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});
