
        * {
            box-sizing: border-box;
        }

        :root {
            --primary-color: #5d5fef;
            --secondary-color: #fff;
            --tertiary-color: #f9fafb;
            --text-color: #151d48;
            --text-secondary-color: #737791;
        }
        .icon{
            width: 50px;
            height: 50px;
        }
        body {
            font-family: "Segoe UI", sans-serif;
            display: flex;
            min-height: 100vh;
            margin: 0;
            background-color: #f4f6f8;
            color: var(--text-color);
            overflow: hidden;
        }

        aside {
            width: 325px;
            background-color: var(--secondary-color);
            padding: 1rem;
            display: flex;
            flex-direction: column;
        }

        .brand {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 2rem;
            gap: 10px;
            color: var(--text-color);
        }

        .brand img {
            width: 32px;
            height: 32px;
            background: var(--primary-color);
            border-radius: 10px;
            padding: 5px;
        }

        nav ul {
            width: 252px;
            list-style: none;
            padding: 0;
            margin: 0;
            flex-grow: 1;
            color: var(--text-secondary-color);
        }

        nav li {
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            flex-direction: row;
            justify-content: flex-start;
        }

        nav a,nav button {
            background-color: transparent;
            border:none;
            cursor: pointer;
            flex-grow: 1;
            display: flex;
            align-items: center;
            text-decoration: none;
            padding: 14px 24px;
            border-radius: 8px;
            transition: background 0.3s, color 0.3s;
        }

        nav a:hover,
        nav a.active,nav button:hover  {
            background-color: var(--primary-color);
            color: var(--secondary-color);
        }

        nav a img, nav button img {
            width: 20px;
            margin-right: 12px;
        }

        nav a:hover img,
        nav button:hover img,
        nav a.active img {
            filter: brightness(0) invert(1);
        }

        main {
               background-color: var(--tertiary-color);
               overflow: hidden;

        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
            padding: 1rem 2rem;
            background: var(--secondary-color);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        header h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-color);
            margin: 0;
        }

        /* Search Bar */
        .search-bar-container {
            flex: 1;
            max-width: 300px;
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-bar-container input {
            width: 100%;
            padding: 10px 38px 10px 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: var(--tertiary-color);
            font-size: 0.9rem;
            color: var(--text-color);
        }

        .search-bar-container img {
            position: absolute;
            right: 12px;
            width: 16px;
            height: 16px;
            pointer-events: none;
        }

        /* Language Select */
        header select {
            padding: 10px 12px;
            border-radius: 8px;
            background: var(--tertiary-color);
            border: 1px solid #ddd;
            font-size: 0.9rem;
            color: var(--text-color);
        }

        /* Profile Menu */
        .profile_menu {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
        }

        .profile_info {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .profile_menu img:first-child {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .profile_menu h5 {
            margin: 0;
            font-size: 0.95rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .profile_menu h6 {
            margin: 0;
            font-size: 0.75rem;
            font-weight: 400;
            color: var(--text-secondary-color);
        }

        .profile_menu img:last-child {
            width: 12px;
            height: 12px;
        }

        /* Sales Section Wrapper */
        main.sections {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-auto-rows: auto;
            gap: 1rem;
        }

        main.sections section:nth-child(1) {
            grid-column: span 2;
        }

        .sales_section {
            border-radius: 10px;
            flex-grow: 0.5;
            flex-basis: 48%;
            padding: 2rem;
            background-color: var(--secondary-color);
        }



        .chart_section {
            border-radius: 10px;
            flex-grow: 0.5;
            flex-basis: 48%;
            padding: 2rem;
            background-color: var(--secondary-color);
        }

        /* Section Header */
        .section_header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .section_header_left h4,
        .chart_section h4 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .section_header_left span {
            font-size: 0.85rem;
            color: var(--text-secondary-color);
        }

        /* Export Button */
        .section_header button {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--secondary-color);
            border: 1px groove var(--text-color);
            background-color: var(--tertiary-color);
            color: #151d48;
            border-radius: 8px;
            padding: 10px 16px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .section_header button:hover {
            background-color: #ededf5;
        }

        .section_header button img {
            width: 16px;
            height: 16px;
        }

        /* Sales Cards Grid */
        .sales_cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 1.5rem;
        }

        /* Individual Card */
        .sales_card {
            border-radius: 12px;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            transition: transform 0.2s ease;
        }

        .sales_card:nth-child(1) {
            background-color: #ffe2e5;
        }

        .sales_card:nth-child(2) {
            background-color: #fff4de;
        }

        .sales_card:nth-child(3) {
            background-color: #dcfce7;
        }

        .sales_card:nth-child(4) {
            background-color: #f3e8ff;
        }

        .sales_card:hover {
            transform: translateY(-4px);
        }

        /* Icon */
        .sales_card img {
            width: 40px;
            height: 40px;
            background-color: #fa5a7d;
            padding: 6px;
            border-radius: 8px;
        }

        .sales_card:nth-child(2) img {
            background-color: #ff947a;
        }

        .sales_card:nth-child(3) img {
            background-color: #3cd856;
        }

        .sales_card:nth-child(4) img {
            background-color: #bf83ff;
        }

        /* Card Content */
        .sales_card_content {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .sales_card_content h3 {
            margin: 0;
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .sales_card_content h5 {
            margin: 0;
            font-size: 0.85rem;
            font-weight: 600;
            color: var(--text-secondary);
        }

        /* Growth Text */
        .sales_card h6 {
            margin: 0;
            font-size: 0.75rem;
            color: #4079ed;
            font-weight: 500;
        }

        .custom-legend {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 20px;
            font-family: 'Segoe UI', sans-serif;
        }

        .legend-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fff;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease, opacity 0.3s;
        }


        .legend-item.inactive {
            opacity: 0.5;
        }

        .legend-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .legend-left img {
            width: 36px;
            height: 36px;
            object-fit: contain;
        }

        .legend-left span {
            font-size: 14px;
            color: #737791;
            display: block;
        }

        .legend-left strong {
            font-size: 16px;
            font-weight: 600;
            color: #151D48;
        }

        .legend-right {
            font-size: 18px;
            font-weight: bold;
            color: #151D48;
        }

        .table_section {
            padding: 2rem;
            background: var(--secondary-color);
            border-radius: 10px;
            flex-grow: 0.5;
            flex-basis: 48%;
        }

        .table_section table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .table_section th,
        .table_section td {
            text-align: left;
            padding: 12px 8px;
            border-bottom: 1px solid #ddd;
        }

        .table_section th {
            color: var(--text-secondary-color);
            font-weight: 600;
        }

        .table_section progress {
            width: 100%;
            height: 10px;
            appearance: none;
            -webkit-appearance: none;
        }

        .table_section progress::-webkit-progress-bar {
            background-color: #eee;
            border-radius: 6px;
        }

        .table_section progress::-webkit-progress-value {
            background-color: var(--primary-color);
            border-radius: 6px;
        }

        .dashboard {
              display: flex;
                flex-direction: column;
                gap: 2rem;
                height: calc(100vh - 70px);
                overflow-y: scroll;
                padding: 15px;
        }

        /* Override for new grid layout */
        .dashboard {
            display: block;
        }

        .row {
            display: grid;
            gap: 1.5rem;
        }

        .row-1 {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .row-2 {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }

        .row-3 {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .card {
            background: #fff;
            padding: 1rem;
            border-radius: 0.75rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            height: 100%;
        }

        .custom-legend {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .legend-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            background: #f9f9f9;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: 0.3s ease;
        }

        .legend-item.inactive {
            opacity: 0.5;
        }

        .legend-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .legend-left img {
            width: 32px;
            height: 32px;
        }

        .legend-left span {
            display: block;
            font-size: 0.875rem;
            color: #333;
        }

        .legend-left strong {
            font-size: 0.75rem;
            color: #888;
        }

        .legend-right {
            font-weight: bold;
            font-size: 1rem;
            color: #333;
        }

        @media (max-width: 768px) {
            .dashboard-grid-secondary,
            .dashboard-grid-tertiary {
                grid-template-columns: 1fr;
            }
        }

        /* Accessibility and Modern Web Standards */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--primary-color);
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 1000;
            transition: top 0.3s;
        }

        .skip-link:focus {
            top: 6px;
        }

        .visually-hidden {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus indicators for better accessibility */
        a:focus,
        button:focus,
        input:focus,
        select:focus,
        [tabindex]:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Sign out button styling */
        /* .sign-out-btn {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            width: 100%;
            text-align: left;
            cursor: pointer;
            border-radius: 10px;
            transition: background-color 0.3s;
        }

        .sign-out-btn:hover {
            background-color: var(--tertiary-color);
        } */

        /* Loading indicator */
        .loading-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Error boundary */
        .error-boundary {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1001;
            text-align: center;
            max-width: 400px;
        }

        /* Performance optimizations */
        img {
            content-visibility: auto;
        }

        .chart-card {
            contain: layout style paint;
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            :root {
                --primary-color: #000080;
                --text-color: #000000;
                --text-secondary-color: #333333;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #7c7fff;
                --secondary-color: #1a1a1a;
                --tertiary-color: #2a2a2a;
                --text-color: #ffffff;
                --text-secondary-color: #cccccc;
            }

            body {
                background-color: #121212;
            }
        }

        /* New class names to match updated HTML */
        .sales-section {
            border-radius: 10px;
            flex-grow: 0.5;
            flex-basis: 48%;
            padding: 2rem;
            background-color: var(--secondary-color);
        }

        .chart-card {
            border-radius: 10px;
            flex-grow: 0.5;
            flex-basis: 48%;
            padding: 1.25rem;
            background-color: var(--secondary-color);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .section-header-left h2,
        .chart-card h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .section-description {
            font-size: 0.85rem;
            color: var(--text-secondary-color);
        }

        .export-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--secondary-color);
            border: 1px groove var(--text-color);
            background-color: var(--tertiary-color);
            color: #151d48;
            border-radius: 8px;
            padding: 10px 16px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .export-btn:hover {
            background-color: #ededf5;
        }

        .export-btn img {
            width: 16px;
            height: 16px;
        }

        .sales-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 1.5rem;
        }

        .sales-card {
            border-radius: 12px;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            transition: transform 0.2s ease;
        }

        .sales-card:nth-child(1) {
            background-color: #ffe2e5;
        }

        .sales-card:nth-child(2) {
            background-color: #fff4de;
        }

        .sales-card:nth-child(3) {
            background-color: #dcfce7;
        }

        .sales-card:nth-child(4) {
            background-color: #f3e8ff;
        }

        .sales-card:hover {
            transform: translateY(-4px);
        }

        .sales-card img {
            width: 40px;
            height: 40px;
            background-color: #fa5a7d;
            padding: 6px;
            border-radius: 8px;
        }

        .sales-card:nth-child(2) img {
            background-color: #ff947a;
        }

        .sales-card:nth-child(3) img {
            background-color: #3cd856;
        }

        .sales-card:nth-child(4) img {
            background-color: #bf83ff;
        }

        .sales-card-content {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .metric-value {
            margin: 0;
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .metric-label {
            margin: 0;
            font-size: 0.85rem;
            font-weight: 600;
            color: var(--text-secondary-color);
        }

        .metric-change {
            margin: 0;
            font-size: 0.75rem;
            color: #4079ed;
            font-weight: 500;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .dashboard-grid-secondary {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .dashboard-grid-tertiary {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .table-section {
            overflow-x: auto;
        }

        .table-container {
            max-height: 400px;
            overflow-y: auto;
        }

        /* Header improvements */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-bar-container {
            display: flex;
            align-items: center;
            background: white;
            border-radius: 8px;
            
            border: 1px solid #e5e7eb;
        }

        .search-bar-container input {
            border: none;
            outline: none;
            padding: 8px 12px;
            font-size: 0.9rem;
        }

        .search-bar-container button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
        }

        .language-selector select {
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            background: white;
            font-size: 0.9rem;
        }

        .profile-menu {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .profile-menu:hover {
            background-color: #f9fafb;
        }

        .profile-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .profile-name {
            font-weight: 600;
            font-size: 0.9rem;
            color: var(--text-color);
        }

        .profile-role {
            font-size: 0.8rem;
            color: var(--text-secondary-color);
        }

        /* Footer */
        .page-footer {
            margin-top: 2rem;
            padding: 1rem 0;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            color: var(--text-secondary-color);
        }

        .page-footer nav ul {
            display: flex;
            gap: 1rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .page-footer nav a {
            color: var(--text-secondary-color);
            text-decoration: none;
        }

        .page-footer nav a:hover {
            color: var(--primary-color);
        }
