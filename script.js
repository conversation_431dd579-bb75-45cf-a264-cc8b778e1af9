// Chart Initialization Function
function initChart() {
  // --- Chart 1: Spline Line ---
  Highcharts.chart('container', {
    chart: { type: 'spline' },
    title: { text: null },
    xAxis: { categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'] },
    yAxis: { title: { text: null } },
    plotOptions: { spline: { marker: { enabled: false } } },
    colors: ['#A700FF', '#EF4444', '#3CD856'],
    series: [
      { name: 'Loyal Customers', data: [100, 200, 150, 500, 100, 600] },
      { name: 'New Customers', data: [500, 100, 600, 100, 200, 150] },
      { name: 'Unique Customers', data: [500, 100, 200, 500, 100, 600] }
    ]
  });

  // --- Chart 2: Column Chart ---
  Highcharts.chart('container2', {
    chart: { type: 'column' },
    title: { text: null },
    xAxis: {
      categories: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
      crosshair: true,
      accessibility: { description: 'Countries' }
    },
    yAxis: { min: 0, title: null },
    tooltip: { valueSuffix: ' (1000 MT)' },
    plotOptions: { column: { pointPadding: 0.2, borderWidth: 0 } },
    series: [
      { name: 'Online Sales', data: [387749, 280000, 129000, 64300, 54000, 34300] },
      { name: 'Offline Sale', data: [45321, 140000, 10000, 140500, 19500, 113500] }
    ]
  });

  // --- Chart 3: Area Spline with Custom Legend ---
  Highcharts.chart('container3', {
    chart: {
      type: 'areaspline',
      backgroundColor: 'transparent',
      spacing: [20, 20, 20, 20],
      style: { fontFamily: 'Segoe UI, sans-serif' }
    },
    title: { text: null },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      layout: 'horizontal',
      useHTML: true,
      labelFormatter: function () {
        if (this.name === 'Last Month') {
          return `<span style="color: #737791;">Last Month<br><strong>$3,004</strong></span>`;
        }
        if (this.name === 'This Month') {
          return `<span style="color: #737791;">This Month<br><strong>$4,504</strong></span>`;
        }
        return this.name;
      }
    },
    xAxis: { labels: { enabled: false }, lineColor: '#eaeaea', tickLength: 0 },
    yAxis: { title: { text: null }, labels: { enabled: false }, gridLineWidth: 0 },
    tooltip: { shared: true, valuePrefix: '$' },
    colors: ['#228BE6', '#00B894'],
    plotOptions: {
      areaspline: {
        fillOpacity: 0.2,
        marker: { enabled: true, radius: 4, symbol: 'circle' },
        lineWidth: 2
      }
    },
    series: [
      { name: 'Last Month', data: [1477, 1489, 2380, 1750, 1708, 3708, 408] },
      { name: 'This Month', data: [9350, 4330, 4310, 6495, 4477, 7489, 6380] }
    ]
  });

  // --- Chart 4: Column with Toggleable Series ---
  const chart4 = Highcharts.chart('container4', {
    chart: { type: 'column' },
    title: { text: null },
    xAxis: { categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'] },
    legend: { enabled: false },
    yAxis: { min: 0, title: null, labels: { enabled: false } },
    tooltip: { valueSuffix: ' (1000 MT)' },
    plotOptions: { column: { pointPadding: 0.2, borderWidth: 0 } },
    colors: ['#4AB58E', '#FFCF00'],
    series: [
      { name: 'Reality Sales', data: [387749, 280000, 129000, 64300, 54000, 34300, 129000] },
      { name: 'Target Sale', data: [45321, 140000, 10000, 140500, 19500, 113500, 19500] }
    ]
  });

  // --- Chart 5: World Map ---
const presenceData = [
  { 'hc-key': 'in', value: 1, color: '#2b908f' },   // India
  { 'hc-key': 'ru', value: 2, color: '#90ee7e' },   // Russia
  { 'hc-key': 'za', value: 3, color: '#f45b5b' },   // South Africa
  { 'hc-key': 'ca', value: 4, color: '#7798BF' },   // Canada
  { 'hc-key': 'au', value: 5, color: '#aaeeee' }    // Australia
];



  Highcharts.mapChart('container5', {
    chart: { map: 'custom/world' },
    title: { text: null },
    subtitle: { text: null },
    mapNavigation: {
      enabled: false,
    },
    tooltip: {
      headerFormat: '',
      pointFormat: '{point.name}'
    },
    xAxis: { enabled: false, visible: false },
    yAxis: { enabled: false, visible: false },
    legend: { enabled: false },
    series: [
      {
        data: presenceData,
        mapData: Highcharts.maps['custom/world'],
        joinBy: 'hc-key',
        name: 'Company Presence',
        states: {
          hover: { color: undefined }
        }
      }
    ]
  });
// Data retrieved from https://en.wikipedia.org/wiki/Winter_Olympic_Games
Highcharts.chart('container6', {
    chart: {
        type: 'column'
    },
    title: {
        text: null,
        align: 'left'
    },
    xAxis: { enabled: false, visible: false },
    yAxis: { enabled: false, visible: false },
    legend: { enabled: false },
    plotOptions: {
        column: {
            stacking: 'normal'
        }
    },

    series: [{
        name: 'Norway',
        data: [148, 133, 124,124],
        stack: 'Europe'
    }, {
        name: 'Germany',
        data: [102, 98, 65,12],
        stack: 'Europe'
    }]
});

  // --- Toggle Chart4 Series on Custom Legend Click ---
  document.querySelectorAll('.legend-item').forEach(item => {
    item.addEventListener('click', function () {
      const index = parseInt(this.dataset.series);
      const series = chart4.series[index];
      if (series.visible) {
        series.hide();
        this.classList.add('inactive');
      } else {
        series.show();
        this.classList.remove('inactive');
      }
    });
  });
}

// Error handling
function showError(message) {
  const errorBoundary = document.getElementById('error-boundary');
  if (errorBoundary) {
    errorBoundary.style.display = 'block';
    errorBoundary.querySelector('p').textContent = message;
  }
}

function hideError() {
  const errorBoundary = document.getElementById('error-boundary');
  if (errorBoundary) {
    errorBoundary.style.display = 'none';
  }
}

// Loading indicator
function showLoading() {
  const loadingIndicator = document.getElementById('loading-indicator');
  if (loadingIndicator) {
    loadingIndicator.style.display = 'flex';
  }
}

function hideLoading() {
  const loadingIndicator = document.getElementById('loading-indicator');
  if (loadingIndicator) {
    loadingIndicator.style.display = 'none';
  }
}

// Enhanced chart initialization with error handling
function initChartSafely() {
  try {
    showLoading();

    // Check if Highcharts is available
    if (typeof Highcharts === 'undefined') {
      throw new Error('Highcharts library is not loaded');
    }

    initChart();
    hideLoading();
    hideError();
  } catch (error) {
    console.error('Chart initialization failed:', error);
    hideLoading();
    showError('Failed to load charts. Please refresh the page.');
  }
}






// Global error handler
window.addEventListener('error', function(e) {
  console.error('Global error:', e.error);
  showError('An unexpected error occurred. Please refresh the page.');
});

window.addEventListener('unhandledrejection', function(e) {
  console.error('Unhandled promise rejection:', e.reason);
  showError('A network error occurred. Please check your connection.');
});

// Initialize everything on DOM Ready
document.addEventListener('DOMContentLoaded', function () {
  try {
    initChartSafely()
  } catch (error) {
    console.error('Initialization failed:', error);
    showError('Failed to initialize the dashboard. Please refresh the page.');
  }
});


